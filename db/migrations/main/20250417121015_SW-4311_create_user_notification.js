
exports.up = function(knex) {
    return knex.schema.raw(`
        -- CREATE TYPE "user_notification_message_type" ----------------------------------------------------------------
        CREATE TYPE public.user_notification_message_type AS ENUM( 'error', 'warning', 'info', 'success' );
        -- -------------------------------------------------------------------------------------------------------------
        
        
        -- CREATE TABLE "user_notifications" ---------------------------------------------------------------------------
        CREATE TABLE IF NOT EXISTS public.user_notifications
        (
            user_notification_id   INT GENERATED ALWAYS AS IDENTITY,
            user_id                INT NOT NULL,
            message_type           user_notification_message_type NOT NULL,
            message_text           TEXT NOT NULL,
            created_at                TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            modified_at               TIMESTAMP WITH TIME ZONE,
            is_read                BOOLEAN DEFAULT FALSE,
            additional_data        JSONB DEFAULT '{}'::JSONB
        );
        
        COMMENT ON TABLE public.user_notifications IS 'Stores notifications for users';
        COMMENT ON COLUMN public.user_notifications.user_id IS 'User ID';
        COMMENT ON COLUMN public.user_notifications.message_type IS 'Type of notification';
        COMMENT ON COLUMN public.user_notifications.message_text IS 'The actual notification message to be displayed';
        COMMENT ON COLUMN public.user_notifications.is_read IS 'Flag that indicates whether notification was read or not';
        COMMENT ON COLUMN public.user_notifications.additional_data IS 'Additional data for notification';

        -- -------------------------------------------------------------------------------------------------------------
        
        
        -- Add indexes to user_notifications table ---------------------------------------------------------------------
        CREATE INDEX IF NOT EXISTS user_notifications_user_id_index ON public.user_notifications (user_id);

        -- -------------------------------------------------------------------------------------------------------------
        
        -- Add modified_at trigger to user_notifications table ---------------------------------------------------------
        CREATE OR REPLACE FUNCTION update_modified_at_column()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.modified_at = NOW();
            RETURN NEW;
        END;
        $$ language 'plpgsql';
        
        CREATE TRIGGER update_user_notifications_modified_at
            BEFORE UPDATE
            ON public.user_notifications
            FOR EACH ROW
        EXECUTE PROCEDURE
            update_modified_at_column();
        -- -------------------------------------------------------------------------------------------------------------
    `)
};

exports.down = function(knex) {
    return knex.schema.raw(`
        DROP TABLE IF EXISTS public.user_notifications;
        DROP TYPE IF EXISTS public.user_notification_message_type;
    `)
};

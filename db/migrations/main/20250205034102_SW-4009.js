exports.up = function(knex) {
    return knex.schema.raw(`
        -- CREATE TABLE "sales_hub_refund" ---------------------------------------------------------------
        CREATE TABLE "public"."sales_hub_refund"
            (
                "sales_hub_refund_id" INT GENERATED ALWAYS AS IDENTITY,
                "id_at_sales_hub" TEXT UNIQUE,
                "sales_hub_payment_id" TEXT NOT NULL,
                "amount" NUMERIC NOT NULL,
                "marketplace_fee" NUMERIC NOT NULL,
                "is_manual" BOOLEAN NOT NULL,
                "status" TEXT NOT NULL
            );
        ----------------------------------------------------------------------------------------------------------------
    `);
};

exports.down = function(knex) {
    return knex.schema.raw(`
        DROP TABLE IF EXISTS "public"."sales_hub_refund";
    `);
};
